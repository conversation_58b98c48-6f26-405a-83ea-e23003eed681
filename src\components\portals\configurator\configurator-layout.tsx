"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Rocket, 
  CheckCircle, 
  AlertTriangle,
  Clock
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

export type ConfigSection = 
  | 'general'
  | 'access'
  | 'content'
  | 'dashboard'
  | 'chatbot'
  | 'api'
  | 'questionnaire'
  | 'stakeholders'

interface ConfiguratorLayoutProps {
  children: React.ReactNode
  portalId: string
  portalName?: string
  currentSection: ConfigSection
  onSectionChange: (section: ConfigSection) => void
  isDraft?: boolean
  hasUnsavedChanges?: boolean
  validationErrors?: string[]
  onSave?: () => void
  onPreview?: () => void
  onDeploy?: () => void
}

const configSections: Array<{
  id: ConfigSection
  label: string
  description: string
}> = [
  { id: 'general', label: 'General Settings', description: 'Basic portal configuration' },
  { id: 'access', label: 'Access & Security', description: 'Authentication and permissions' },
  { id: 'content', label: 'Content & Data', description: 'Frameworks and data exposure' },
  { id: 'dashboard', label: 'Dashboard Design', description: 'Layout and customization' },
  { id: 'chatbot', label: 'Chatbot Configuration', description: 'AI assistant settings' },
  { id: 'api', label: 'API Settings', description: 'API endpoints and access' },
  { id: 'questionnaire', label: 'Questionnaire Automation', description: 'Self-service forms' },
  { id: 'stakeholders', label: 'Stakeholder Management', description: 'User access and roles' },
]

export function ConfiguratorLayout({
  children,
  portalId,
  portalName = "New Portal",
  currentSection,
  onSectionChange,
  isDraft = true,
  hasUnsavedChanges = false,
  validationErrors = [],
  onSave,
  onPreview,
  onDeploy
}: ConfiguratorLayoutProps) {
  const router = useRouter()
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  const handleSave = async () => {
    if (onSave) {
      await onSave()
      setLastSaved(new Date())
    }
  }

  const canDeploy = validationErrors.length === 0 && !hasUnsavedChanges

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Configurator Header - sticky positioned under system top bar */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-12 z-40 shrink-0">
        <div className="px-4 lg:px-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 py-3 lg:h-16 lg:py-0">
            {/* Left side - Back button and breadcrumb */}
            <div className="flex items-center gap-3 lg:gap-4 min-w-0 flex-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/portals')}
                className="gap-2 shrink-0"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden lg:inline">Back to Dashboard</span>
                <span className="lg:hidden">Back</span>
              </Button>
              <Separator orientation="vertical" className="h-6 hidden lg:block" />
              <div className="min-w-0 flex-1">
                <h1 className="font-semibold text-lg lg:text-xl truncate">{portalName}</h1>
                <div className="flex items-center gap-2 text-sm text-muted-foreground flex-wrap">
                  <span className="hidden lg:inline">Portal Configuration</span>
                  <span className="lg:hidden">Config</span>
                  {isDraft && (
                    <Badge variant="secondary" className="text-xs">
                      Draft
                    </Badge>
                  )}
                  {hasUnsavedChanges && (
                    <Badge variant="warning" className="text-xs">
                      Unsaved
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Right side - Actions */}
            <div className="flex items-center gap-2 shrink-0">
              {lastSaved && (
                <div className="hidden xl:flex items-center gap-1 text-xs text-muted-foreground mr-3">
                  <Clock className="h-3 w-3" />
                  <span>Saved {lastSaved.toLocaleTimeString()}</span>
                </div>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={onPreview}
                className="gap-2"
              >
                <Eye className="h-4 w-4" />
                <span className="hidden lg:inline">Preview</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                disabled={!hasUnsavedChanges}
                className="gap-2"
              >
                <Save className="h-4 w-4" />
                <span className="hidden lg:inline">Save</span>
              </Button>

              <Button
                size="sm"
                onClick={onDeploy}
                disabled={!canDeploy}
                className="gap-2"
              >
                <Rocket className="h-4 w-4" />
                <span className="hidden lg:inline">{isDraft ? 'Deploy' : 'Update'}</span>
              </Button>
            </div>
          </div>

          {/* Section Navigation */}
          <div className="pb-4 border-t border-border/50 pt-4 mt-2">
            <div className="flex items-center gap-1 overflow-x-auto scrollbar-hide">
              {configSections.map((section, index) => {
                const isActive = currentSection === section.id
                const isCompleted = false // TODO: Add completion logic

                return (
                  <Button
                    key={section.id}
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    onClick={() => onSectionChange(section.id)}
                    className={cn(
                      "flex items-center gap-2 whitespace-nowrap shrink-0 text-sm font-medium",
                      "h-9 px-3 lg:px-4",
                      isActive && "bg-primary text-primary-foreground shadow-sm"
                    )}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : validationErrors.some(error => error.includes(section.id)) ? (
                      <AlertTriangle className="h-4 w-4" />
                    ) : (
                      <span className="flex h-4 w-4 items-center justify-center text-xs font-semibold bg-background/20 rounded-full">
                        {index + 1}
                      </span>
                    )}
                    <span className="hidden lg:inline">{section.label}</span>
                    <span className="lg:hidden">{section.label.split(' ')[0]}</span>
                  </Button>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - scrollable area with proper constraints */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 lg:px-6 py-6 lg:py-8">
          {/* Section Header */}
          <div className="mb-8">
            <h2 className="text-2xl lg:text-3xl font-bold tracking-tight">
              {configSections.find(s => s.id === currentSection)?.label}
            </h2>
            <p className="text-muted-foreground mt-2 text-base lg:text-lg">
              {configSections.find(s => s.id === currentSection)?.description}
            </p>
          </div>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="mb-8 p-4 lg:p-6 border border-destructive/20 bg-destructive/5 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <AlertTriangle className="h-5 w-5 text-destructive" />
                <h3 className="font-semibold text-destructive">Configuration Issues</h3>
              </div>
              <ul className="text-sm text-destructive space-y-2">
                {validationErrors.map((error, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-destructive mt-0.5">•</span>
                    <span>{error}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Section Content */}
          <div className="space-y-8">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}
