"use client"

import { useState } from "react"
import { Plus, Trash2, Shield, Key, Clock, Users } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface AccessSecurityProps {
  data: {
    authMethod: 'sso' | 'email' | 'custom'
    allowedDomains: string[]
    sessionTimeout: number
    mfaRequired: boolean
  }
  onChange: (data: any) => void
}

const authMethods = [
  { value: 'email', label: 'Email Authentication', description: 'Simple email-based login' },
  { value: 'sso', label: 'Single Sign-On', description: 'SAML/OIDC integration' },
  { value: 'custom', label: 'Custom Authentication', description: 'Custom auth provider' }
]

export function AccessSecurity({ data, onChange }: AccessSecurityProps) {
  const [newDomain, setNewDomain] = useState('')

  const handleInputChange = (field: string, value: any) => {
    onChange({
      ...data,
      [field]: value
    })
  }

  const addDomain = () => {
    if (newDomain.trim() && !data.allowedDomains.includes(newDomain.trim())) {
      handleInputChange('allowedDomains', [...data.allowedDomains, newDomain.trim()])
      setNewDomain('')
    }
  }

  const removeDomain = (domain: string) => {
    handleInputChange('allowedDomains', data.allowedDomains.filter(d => d !== domain))
  }

  const toggleMFA = () => {
    handleInputChange('mfaRequired', !data.mfaRequired)
  }

  return (
    <div className="space-y-6 w-full max-w-full overflow-hidden">
      {/* Authentication Method */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Authentication Method
          </CardTitle>
          <CardDescription>
            Configure how users will authenticate to access the portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Authentication Type</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-start">
                  {authMethods.find(method => method.value === data.authMethod)?.label || 'Select authentication method'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[300px] max-w-[90vw]">
                {authMethods.map((method) => (
                  <DropdownMenuItem
                    key={method.value}
                    onClick={() => handleInputChange('authMethod', method.value)}
                  >
                    <div>
                      <div className="font-medium">{method.label}</div>
                      <div className="text-sm text-muted-foreground">{method.description}</div>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {data.authMethod === 'sso' && (
            <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">SSO Configuration</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                SSO integration requires additional configuration. Contact your administrator to set up SAML or OIDC providers.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Domain Restrictions */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Domain Restrictions
          </CardTitle>
          <CardDescription>
            Restrict access to specific email domains for enhanced security
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 w-full">
            <Input
              placeholder="example.com"
              value={newDomain}
              onChange={(e) => setNewDomain(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addDomain()}
              className="flex-1 min-w-0"
            />
            <Button onClick={addDomain} disabled={!newDomain.trim()} className="shrink-0">
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>

          {data.allowedDomains.length > 0 && (
            <div className="space-y-2">
              <Label>Allowed Domains</Label>
              <div className="flex flex-wrap gap-2">
                {data.allowedDomains.map((domain) => (
                  <Badge key={domain} variant="secondary" className="flex items-center gap-1">
                    {domain}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => removeDomain(domain)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {data.allowedDomains.length === 0 && (
            <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                No domain restrictions configured. Anyone with the portal URL can attempt to access it.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Session Management */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Session Management
          </CardTitle>
          <CardDescription>
            Configure session timeout and security settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="session-timeout">Session Timeout (seconds)</Label>
            <Input
              id="session-timeout"
              type="number"
              value={data.sessionTimeout}
              onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value) || 3600)}
              min="300"
              max="86400"
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Current setting: {Math.floor(data.sessionTimeout / 60)} minutes
            </p>
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">Multi-Factor Authentication</h4>
              <p className="text-sm text-muted-foreground">
                Require additional verification for enhanced security
              </p>
            </div>
            <Button
              variant={data.mfaRequired ? "default" : "outline"}
              onClick={toggleMFA}
              className="gap-2"
            >
              <Shield className="h-4 w-4" />
              {data.mfaRequired ? 'Enabled' : 'Disabled'}
            </Button>
          </div>

          {data.mfaRequired && (
            <div className="p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
              <p className="text-sm text-green-700 dark:text-green-300">
                MFA is enabled. Users will be required to provide additional verification during login.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Summary */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Security Summary
          </CardTitle>
          <CardDescription>
            Overview of current security configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-4 gap-4 w-full">
            <div className="space-y-2 min-w-0">
              <Label className="text-sm">Authentication</Label>
              <Badge variant="outline" className="w-full justify-center text-xs">
                {authMethods.find(m => m.value === data.authMethod)?.label}
              </Badge>
            </div>

            <div className="space-y-2 min-w-0">
              <Label className="text-sm">Domain Restrictions</Label>
              <Badge
                variant={data.allowedDomains.length > 0 ? "success" : "warning"}
                className="w-full justify-center text-xs"
              >
                {data.allowedDomains.length > 0 ? `${data.allowedDomains.length} domains` : 'Open access'}
              </Badge>
            </div>

            <div className="space-y-2 min-w-0">
              <Label className="text-sm">Session Timeout</Label>
              <Badge variant="outline" className="w-full justify-center text-xs">
                {Math.floor(data.sessionTimeout / 60)}m
              </Badge>
            </div>

            <div className="space-y-2 min-w-0">
              <Label className="text-sm">Multi-Factor Auth</Label>
              <Badge
                variant={data.mfaRequired ? "success" : "secondary"}
                className="w-full justify-center text-xs"
              >
                {data.mfaRequired ? 'Required' : 'Optional'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
